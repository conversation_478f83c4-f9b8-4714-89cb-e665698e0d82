#!/usr/bin/env python3
"""
Test script for the GPX to Image Generator
"""

import os
from gpx_to_image import GPXToImageGenerator

def test_generator():
    """Test the GPX to Image Generator with the sample file."""
    
    print("Testing GPX to Image Generator...")
    print("=" * 50)
    
    # Check if sample file exists
    sample_file = "sample_route.gpx"
    if not os.path.exists(sample_file):
        print(f"Error: Sample file '{sample_file}' not found")
        return False
    
    # Create generator instance
    generator = GPXToImageGenerator(width=1080, height=1920)
    
    # Parse the GPX file
    print(f"Parsing GPX file: {sample_file}")
    if not generator.parse_gpx_file(sample_file):
        print("Failed to parse GPX file")
        return False
    
    print(f"✓ Successfully parsed {len(generator.track_points)} track points")
    print(f"✓ Total route distance: {generator.total_distance:.2f} km")
    
    # Calculate distance markers
    markers = generator._get_distance_markers()
    print(f"✓ Generated {len(markers)} distance markers (every 100km)")
    
    # Generate the image
    output_file = "test_route_image.png"
    print(f"Generating image: {output_file}")
    
    if not generator.generate_image(output_file):
        print("Failed to generate image")
        return False
    
    print(f"✓ Image successfully saved to: {output_file}")
    
    # Verify file was created
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file)
        print(f"✓ Output file size: {file_size:,} bytes")
    else:
        print("✗ Output file was not created")
        return False
    
    print("\n" + "=" * 50)
    print("✓ All tests passed successfully!")
    print("\nYou can now use the generator with your own GPX files:")
    print("python gpx_to_image.py your_route.gpx -o your_image.png")
    
    return True

if __name__ == "__main__":
    success = test_generator()
    exit(0 if success else 1)
