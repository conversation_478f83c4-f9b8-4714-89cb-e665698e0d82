#!/usr/bin/env python3
"""
GPX to Image Generator - Interactive GUI Version

A graphical interface for converting GPX route files into vertical PNG images 
suitable for social media sharing.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
from pathlib import Path
from gpx_to_image import GPXToImageGenerator


class GPXToImageGUI:
    """GUI application for the GPX to Image Generator."""
    
    def __init__(self, root):
        """Initialize the GUI application."""
        self.root = root
        self.root.title("GPX to Image Generator")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # Variables
        self.gpx_file_path = tk.StringVar()
        self.output_file_path = tk.StringVar()
        self.image_width = tk.IntVar(value=1080)
        self.image_height = tk.IntVar(value=1920)
        self.processing = False
        
        # Set default output path
        self.output_file_path.set("route_image.png")
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="GPX to Image Generator", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # GPX File Selection
        ttk.Label(main_frame, text="GPX File:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        gpx_entry = ttk.Entry(main_frame, textvariable=self.gpx_file_path, width=50)
        gpx_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=5)
        
        gpx_browse_btn = ttk.Button(main_frame, text="Browse...", 
                                   command=self.browse_gpx_file)
        gpx_browse_btn.grid(row=1, column=2, padx=(5, 0), pady=5)
        
        # Output File Selection
        ttk.Label(main_frame, text="Output File:").grid(row=2, column=0, sticky=tk.W, pady=5)
        
        output_entry = ttk.Entry(main_frame, textvariable=self.output_file_path, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=5)
        
        output_browse_btn = ttk.Button(main_frame, text="Browse...", 
                                      command=self.browse_output_file)
        output_browse_btn.grid(row=2, column=2, padx=(5, 0), pady=5)
        
        # Image Dimensions Frame
        dimensions_frame = ttk.LabelFrame(main_frame, text="Image Dimensions", padding="10")
        dimensions_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), 
                             pady=(20, 10))
        dimensions_frame.columnconfigure(1, weight=1)
        dimensions_frame.columnconfigure(3, weight=1)
        
        # Width
        ttk.Label(dimensions_frame, text="Width:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        width_spinbox = ttk.Spinbox(dimensions_frame, from_=400, to=4000, 
                                   textvariable=self.image_width, width=10)
        width_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        # Height
        ttk.Label(dimensions_frame, text="Height:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        height_spinbox = ttk.Spinbox(dimensions_frame, from_=400, to=4000, 
                                    textvariable=self.image_height, width=10)
        height_spinbox.grid(row=0, column=3, sticky=tk.W)
        
        # Preset buttons
        preset_frame = ttk.Frame(dimensions_frame)
        preset_frame.grid(row=1, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(preset_frame, text="Instagram (1080x1920)", 
                  command=lambda: self.set_dimensions(1080, 1920)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(preset_frame, text="Square (1080x1080)", 
                  command=lambda: self.set_dimensions(1080, 1080)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(preset_frame, text="Landscape (1920x1080)", 
                  command=lambda: self.set_dimensions(1920, 1080)).pack(side=tk.LEFT)
        
        # Features Info
        info_frame = ttk.LabelFrame(main_frame, text="Features", padding="10")
        info_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        features_text = """✓ Minimalist map design with clean styling
✓ Distance markers every 100km along the route
✓ Total distance display at the bottom
✓ Optimized for social media sharing
✓ High-quality PNG output"""
        
        ttk.Label(info_frame, text=features_text, justify=tk.LEFT).pack(anchor=tk.W)
        
        # Progress Bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                           mode='indeterminate')
        self.progress_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), 
                              pady=(20, 10))
        
        # Status Label
        self.status_var = tk.StringVar(value="Ready to generate image")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=6, column=0, columnspan=3, pady=(0, 10))
        
        # Generate Button
        self.generate_btn = ttk.Button(main_frame, text="Generate Image", 
                                      command=self.generate_image, 
                                      style="Accent.TButton")
        self.generate_btn.grid(row=7, column=0, columnspan=3, pady=20)
        
        # Configure button style
        style = ttk.Style()
        style.configure("Accent.TButton", font=("Arial", 12, "bold"))
        
    def set_dimensions(self, width, height):
        """Set image dimensions to preset values."""
        self.image_width.set(width)
        self.image_height.set(height)
        
    def browse_gpx_file(self):
        """Open file dialog to select GPX file."""
        file_path = filedialog.askopenfilename(
            title="Select GPX File",
            filetypes=[
                ("GPX files", "*.gpx"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.gpx_file_path.set(file_path)
            # Auto-generate output filename based on input
            input_name = Path(file_path).stem
            output_name = f"{input_name}_route_image.png"
            self.output_file_path.set(output_name)
            
    def browse_output_file(self):
        """Open file dialog to select output file location."""
        file_path = filedialog.asksaveasfilename(
            title="Save Image As",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.output_file_path.set(file_path)
            
    def update_status(self, message):
        """Update the status label."""
        self.status_var.set(message)
        self.root.update_idletasks()
        
    def generate_image(self):
        """Generate the image in a separate thread."""
        if self.processing:
            return
            
        # Validate inputs
        if not self.gpx_file_path.get():
            messagebox.showerror("Error", "Please select a GPX file")
            return
            
        if not os.path.exists(self.gpx_file_path.get()):
            messagebox.showerror("Error", "Selected GPX file does not exist")
            return
            
        if not self.output_file_path.get():
            messagebox.showerror("Error", "Please specify an output file")
            return
            
        # Start processing in a separate thread
        self.processing = True
        self.generate_btn.config(state="disabled")
        self.progress_bar.start()
        
        thread = threading.Thread(target=self._generate_image_thread)
        thread.daemon = True
        thread.start()
        
    def _generate_image_thread(self):
        """Generate image in background thread."""
        try:
            self.update_status("Parsing GPX file...")
            
            # Create generator
            generator = GPXToImageGenerator(
                width=self.image_width.get(),
                height=self.image_height.get()
            )
            
            # Parse GPX file
            if not generator.parse_gpx_file(self.gpx_file_path.get()):
                raise Exception("Failed to parse GPX file")
                
            self.update_status(f"Loaded {len(generator.track_points)} track points. Generating image...")
            
            # Generate image
            if not generator.generate_image(self.output_file_path.get()):
                raise Exception("Failed to generate image")
                
            # Success
            self.root.after(0, self._generation_complete, True, 
                           f"Image saved successfully!\nTotal distance: {generator.total_distance:.1f} km")
            
        except Exception as e:
            self.root.after(0, self._generation_complete, False, str(e))
            
    def _generation_complete(self, success, message):
        """Handle completion of image generation."""
        self.processing = False
        self.generate_btn.config(state="normal")
        self.progress_bar.stop()
        
        if success:
            self.update_status("Generation completed successfully!")
            messagebox.showinfo("Success", message)
            
            # Ask if user wants to open the output folder
            if messagebox.askyesno("Open Folder", "Would you like to open the folder containing the generated image?"):
                output_dir = os.path.dirname(os.path.abspath(self.output_file_path.get()))
                os.startfile(output_dir)
        else:
            self.update_status("Generation failed")
            messagebox.showerror("Error", f"Failed to generate image:\n{message}")


def main():
    """Main function to run the GUI application."""
    root = tk.Tk()
    app = GPXToImageGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
