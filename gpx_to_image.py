#!/usr/bin/env python3
"""
GPX to Image Generator

A tool to convert GPX route files into vertical PNG images suitable for social media.
Features:
- Minimalist map background
- Distance markers every 100km
- Total distance display
- Vertical format optimized for social media
"""

import gpxpy
import gpxpy.gpx
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import io
import math
from typing import List, Tuple, Optional
import argparse
import os
import warnings
warnings.filterwarnings('ignore')


class GPXToImageGenerator:
    """Main class for converting GPX files to images."""

    def __init__(self, width: int = 1080, height: int = 1920):
        """
        Initialize the generator with image dimensions.

        Args:
            width: Image width in pixels (default: 1080 for social media)
            height: Image height in pixels (default: 1920 for social media)
        """
        self.width = width
        self.height = height
        self.margin = 100  # Margin around the map
        self.track_points = []
        self.track_segments = []  # Store segments separately
        self.original_segments = []  # Store original order for reference
        self.total_distance = 0.0

    def parse_gpx_file(self, gpx_file_path: str) -> bool:
        """
        Parse a GPX file and extract track points.

        Args:
            gpx_file_path: Path to the GPX file

        Returns:
            True if parsing was successful, False otherwise
        """
        try:
            with open(gpx_file_path, 'r', encoding='utf-8') as gpx_file:
                gpx = gpxpy.parse(gpx_file)

            self.track_points = []
            self.track_segments = []
            self.original_segments = []
            self.segment_names = []  # Store segment names

            for track_idx, track in enumerate(gpx.tracks):
                track_name = track.name or f"Track {track_idx + 1}"

                for segment_idx, segment in enumerate(track.segments):
                    segment_points = []
                    for point in segment.points:
                        point_data = {
                            'lat': point.latitude,
                            'lon': point.longitude,
                            'elevation': point.elevation
                        }
                        self.track_points.append(point_data)
                        segment_points.append(point_data)

                    if segment_points:  # Only add non-empty segments
                        # Generate a meaningful segment name
                        segment_name = self._generate_segment_name(
                            track_name, segment_idx + 1, segment_points)

                        self.track_segments.append(segment_points)
                        self.original_segments.append(segment_points.copy())
                        self.segment_names.append(segment_name)

            if not self.track_points:
                print("No track points found in GPX file")
                return False

            self._calculate_total_distance()
            print(f"Loaded {len(self.track_points)} track points")
            print(f"Total distance: {self.total_distance:.2f} km")
            return True

        except Exception as e:
            print(f"Error parsing GPX file: {e}")
            return False

    def _calculate_total_distance(self):
        """Calculate the total distance of the route."""
        self.total_distance = 0.0

        for i in range(1, len(self.track_points)):
            prev_point = self.track_points[i-1]
            curr_point = self.track_points[i]

            distance = self._haversine_distance(
                prev_point['lat'], prev_point['lon'],
                curr_point['lat'], curr_point['lon']
            )
            self.total_distance += distance

    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        Calculate the great circle distance between two points on Earth.

        Returns:
            Distance in kilometers
        """
        R = 6371  # Earth's radius in kilometers

        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)

        a = (math.sin(delta_lat / 2) ** 2 +
             math.cos(lat1_rad) * math.cos(lat2_rad) *
             math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

        return R * c

    def _generate_segment_name(self, track_name: str, segment_num: int, segment_points: List[dict]) -> str:
        """
        Generate a meaningful name for a segment based on its location.

        Args:
            track_name: Name of the parent track
            segment_num: Segment number within the track
            segment_points: List of points in the segment

        Returns:
            A descriptive name for the segment
        """
        if not segment_points:
            return f"{track_name} - Segment {segment_num}"

        start_point = segment_points[0]
        end_point = segment_points[-1]

        # Calculate segment distance
        segment_distance = 0.0
        for i in range(1, len(segment_points)):
            segment_distance += self._haversine_distance(
                segment_points[i-1]['lat'], segment_points[i-1]['lon'],
                segment_points[i]['lat'], segment_points[i]['lon']
            )

        # Try to determine geographical context
        start_desc = self._get_location_description(
            start_point['lat'], start_point['lon'])
        end_desc = self._get_location_description(
            end_point['lat'], end_point['lon'])

        # Create a descriptive name
        if start_desc and end_desc and start_desc != end_desc:
            name = f"{start_desc} → {end_desc}"
        elif start_desc:
            name = f"From {start_desc}"
        elif end_desc:
            name = f"To {end_desc}"
        else:
            name = f"Segment {segment_num}"

        # Add distance info
        name += f" ({segment_distance:.1f} km)"

        return name

    def _get_location_description(self, lat: float, lon: float) -> str:
        """
        Get a simple geographical description based on coordinates.

        Args:
            lat: Latitude
            lon: Longitude

        Returns:
            A simple location description
        """
        # Simple geographical regions (this could be enhanced with reverse geocoding)

        # North America regions
        if -170 <= lon <= -50 and 20 <= lat <= 70:
            if lat >= 49:
                return "Canada"
            elif lat >= 25:
                if lon >= -95:
                    return "Eastern US"
                elif lon >= -115:
                    return "Central US"
                else:
                    return "Western US"
            else:
                return "Mexico/Caribbean"

        # Europe
        elif -15 <= lon <= 40 and 35 <= lat <= 70:
            if lon <= 10:
                return "Western Europe"
            else:
                return "Eastern Europe"

        # Asia
        elif 40 <= lon <= 180 and 10 <= lat <= 70:
            if lon <= 100:
                return "Central Asia"
            elif lat >= 40:
                return "Northern Asia"
            else:
                return "Southeast Asia"

        # Australia/Oceania
        elif 110 <= lon <= 180 and -50 <= lat <= -10:
            return "Australia/Oceania"

        # Africa
        elif -20 <= lon <= 55 and -35 <= lat <= 35:
            if lat >= 0:
                return "Northern Africa"
            else:
                return "Southern Africa"

        # South America
        elif -85 <= lon <= -30 and -60 <= lat <= 15:
            return "South America"

        # Default to coordinates
        return f"{lat:.2f}°, {lon:.2f}°"

    def _get_distance_markers(self) -> List[Tuple[float, float, float]]:
        """
        Calculate positions for distance markers every 100km.

        Returns:
            List of tuples (lat, lon, distance_km)
        """
        markers = []
        current_distance = 0.0
        next_marker_distance = 100.0

        for i in range(1, len(self.track_points)):
            prev_point = self.track_points[i-1]
            curr_point = self.track_points[i]

            segment_distance = self._haversine_distance(
                prev_point['lat'], prev_point['lon'],
                curr_point['lat'], curr_point['lon']
            )
            current_distance += segment_distance

            if current_distance >= next_marker_distance:
                markers.append((
                    curr_point['lat'],
                    curr_point['lon'],
                    next_marker_distance
                ))
                next_marker_distance += 100.0

        return markers

    def get_segment_info(self) -> List[dict]:
        """
        Get information about each segment for reordering.

        Returns:
            List of dictionaries with segment information
        """
        segment_info = []
        for i, segment in enumerate(self.track_segments):
            if segment:
                start_point = segment[0]
                end_point = segment[-1]

                # Calculate segment distance
                segment_distance = 0.0
                for j in range(1, len(segment)):
                    segment_distance += self._haversine_distance(
                        segment[j-1]['lat'], segment[j-1]['lon'],
                        segment[j]['lat'], segment[j]['lon']
                    )

                # Get the segment name
                segment_name = self.segment_names[i] if i < len(
                    self.segment_names) else f"Segment {i+1}"

                segment_info.append({
                    'index': i,
                    'name': segment_name,
                    'points': len(segment),
                    'distance_km': segment_distance,
                    'start_lat': start_point['lat'],
                    'start_lon': start_point['lon'],
                    'end_lat': end_point['lat'],
                    'end_lon': end_point['lon'],
                    'description': f"{segment_name}: {len(segment)} points"
                })

        return segment_info

    def reorder_segments(self, new_order: List[int]):
        """
        Reorder segments according to the provided order.

        Args:
            new_order: List of segment indices in the desired order
        """
        if not new_order or len(new_order) != len(self.track_segments):
            print("Invalid segment order provided")
            return False

        if set(new_order) != set(range(len(self.track_segments))):
            print("Segment order must contain all segment indices exactly once")
            return False

        # Reorder segments and names
        # Copy original names
        original_names = [name for name in self.segment_names]
        self.track_segments = [self.original_segments[i] for i in new_order]
        self.segment_names = [original_names[i] for i in new_order]

        # Rebuild track_points in new order
        self.track_points = []
        for segment in self.track_segments:
            self.track_points.extend(segment)

        # Recalculate total distance
        self._calculate_total_distance()

        print(
            f"Segments reordered successfully. New order: {[i+1 for i in new_order]}")
        return True

    def reset_segment_order(self):
        """Reset segments to their original order."""
        self.track_segments = [seg.copy() for seg in self.original_segments]

        # Reset segment names to original order (regenerate them)
        self.segment_names = []
        for i, segment in enumerate(self.track_segments):
            if segment:
                segment_name = self._generate_segment_name(
                    "Track 1", i + 1, segment)
                self.segment_names.append(segment_name)

        # Rebuild track_points in original order
        self.track_points = []
        for segment in self.track_segments:
            self.track_points.extend(segment)

        # Recalculate total distance
        self._calculate_total_distance()

        print("Segments reset to original order")

    def _create_map_background(self, ax, lats, lons):
        """Create a simple map background with geographical context."""
        # Calculate bounds
        lat_min, lat_max = min(lats), max(lats)
        lon_min, lon_max = min(lons), max(lons)

        # Add some padding
        lat_range = lat_max - lat_min
        lon_range = lon_max - lon_min
        padding = 0.05

        lat_min -= lat_range * padding
        lat_max += lat_range * padding
        lon_min -= lon_range * padding
        lon_max += lon_range * padding

        # Create a more sophisticated map-like background

        # 1. Add coordinate grid (like a real map)
        # Major grid lines every degree or appropriate interval
        lat_interval = max(0.1, lat_range / 6)
        lon_interval = max(0.1, lon_range / 6)

        lat_lines = np.arange(
            np.floor(lat_min / lat_interval) * lat_interval,
            np.ceil(lat_max / lat_interval) * lat_interval + lat_interval,
            lat_interval
        )
        lon_lines = np.arange(
            np.floor(lon_min / lon_interval) * lon_interval,
            np.ceil(lon_max / lon_interval) * lon_interval + lon_interval,
            lon_interval
        )

        # Draw major grid lines
        for lat in lat_lines:
            if lat_min <= lat <= lat_max:
                ax.axhline(y=lat, color='#d1d5db',
                           linewidth=0.8, alpha=0.4, zorder=0)

        for lon in lon_lines:
            if lon_min <= lon <= lon_max:
                ax.axvline(x=lon, color='#d1d5db',
                           linewidth=0.8, alpha=0.4, zorder=0)

        # 2. Add subtle terrain-like features
        np.random.seed(42)  # For consistent results

        # Create "water bodies" - larger blue areas
        num_water = max(2, int((lat_range * lon_range) * 100))
        for _ in range(num_water):
            feat_lat = np.random.uniform(lat_min, lat_max)
            feat_lon = np.random.uniform(lon_min, lon_max)
            size = np.random.uniform(0.002, 0.008)

            circle = plt.Circle((feat_lon, feat_lat), size,
                                color='#bfdbfe', alpha=0.3, zorder=0)
            ax.add_patch(circle)

        # Create "urban areas" - small gray rectangles
        num_urban = max(3, int((lat_range * lon_range) * 150))
        for _ in range(num_urban):
            feat_lat = np.random.uniform(lat_min, lat_max)
            feat_lon = np.random.uniform(lon_min, lon_max)
            width = np.random.uniform(0.001, 0.003)
            height = np.random.uniform(0.001, 0.003)

            rect = plt.Rectangle((feat_lon - width/2, feat_lat - height/2),
                                 width, height,
                                 color='#e5e7eb', alpha=0.4, zorder=0)
            ax.add_patch(rect)

        # 3. Add coordinate labels for geographical context
        # Add a few coordinate labels at the edges
        if lat_range > 0.5:  # Only for larger areas
            # Add latitude labels on the left
            for i, lat in enumerate(lat_lines[::2]):  # Every other line
                if lat_min <= lat <= lat_max and i < 3:
                    ax.text(lon_min + lon_range * 0.02, lat, f'{lat:.1f}°',
                            fontsize=8, color='#6b7280', alpha=0.7,
                            verticalalignment='center', zorder=1)

            # Add longitude labels at the bottom
            for i, lon in enumerate(lon_lines[::2]):  # Every other line
                if lon_min <= lon <= lon_max and i < 3:
                    ax.text(lon, lat_min + lat_range * 0.02, f'{lon:.1f}°',
                            fontsize=8, color='#6b7280', alpha=0.7,
                            horizontalalignment='center', zorder=1)

    def generate_image(self, output_path: str) -> bool:
        """
        Generate the final image with route, markers, and distance info.

        Args:
            output_path: Path where the PNG image will be saved

        Returns:
            True if generation was successful, False otherwise
        """
        if not self.track_points:
            print("No track points available. Parse a GPX file first.")
            return False

        try:
            # Create figure with vertical aspect ratio
            fig, ax = plt.subplots(
                figsize=(self.width/100, self.height/100), dpi=100)

            # Extract coordinates
            lats = [point['lat'] for point in self.track_points]
            lons = [point['lon'] for point in self.track_points]

            # Set up the plot bounds with margin
            lat_range = max(lats) - min(lats)
            lon_range = max(lons) - min(lons)

            # Adjust bounds to maintain aspect ratio and add margin
            margin_factor = 0.1
            ax.set_xlim(min(lons) - lon_range * margin_factor,
                        max(lons) + lon_range * margin_factor)
            ax.set_ylim(min(lats) - lat_range * margin_factor,
                        max(lats) + lat_range * margin_factor)

            # Minimalist styling
            ax.set_facecolor('#f8f9fa')  # Light gray background
            fig.patch.set_facecolor('#ffffff')  # White figure background

            # Remove axes and ticks for clean look
            ax.set_xticks([])
            ax.set_yticks([])
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['bottom'].set_visible(False)
            ax.spines['left'].set_visible(False)

            # Add map background
            self._create_map_background(ax, lats, lons)

            # Plot the route segments separately to avoid straight lines between segments
            for segment in self.track_segments:
                if len(segment) > 1:  # Only plot segments with multiple points
                    seg_lats = [point['lat'] for point in segment]
                    seg_lons = [point['lon'] for point in segment]
                    ax.plot(seg_lons, seg_lats, color='#2563eb',
                            linewidth=3, alpha=0.8, zorder=2)

            # Add start and end markers
            if self.track_points:
                start_point = self.track_points[0]
                end_point = self.track_points[-1]

                # Start marker (green)
                ax.scatter(start_point['lon'], start_point['lat'],
                           color='#10b981', s=150, zorder=4,
                           edgecolors='white', linewidth=3, marker='o')
                ax.annotate('START',
                            (start_point['lon'], start_point['lat']),
                            xytext=(15, 15),
                            textcoords='offset points',
                            fontsize=9,
                            fontweight='bold',
                            color='#065f46',
                            bbox=dict(boxstyle='round,pad=0.3',
                                      facecolor='#d1fae5',
                                      edgecolor='#10b981',
                                      alpha=0.9))

                # End marker (red) - only if different from start
                if len(self.track_points) > 1:
                    ax.scatter(end_point['lon'], end_point['lat'],
                               color='#ef4444', s=150, zorder=4,
                               edgecolors='white', linewidth=3, marker='s')
                    ax.annotate('FINISH',
                                (end_point['lon'], end_point['lat']),
                                xytext=(15, -25),
                                textcoords='offset points',
                                fontsize=9,
                                fontweight='bold',
                                color='#7f1d1d',
                                bbox=dict(boxstyle='round,pad=0.3',
                                          facecolor='#fee2e2',
                                          edgecolor='#ef4444',
                                          alpha=0.9))

            # Add distance markers
            markers = self._get_distance_markers()
            for lat, lon, distance in markers:
                ax.scatter(lon, lat, color='#dc2626', s=100, zorder=3,
                           edgecolors='white', linewidth=2)
                ax.annotate(f'{int(distance)}km',
                            (lon, lat),
                            xytext=(10, 10),
                            textcoords='offset points',
                            fontsize=10,
                            fontweight='bold',
                            color='#1f2937',
                            bbox=dict(boxstyle='round,pad=0.3',
                                      facecolor='white',
                                      edgecolor='none',
                                      alpha=0.8))

            # Add total distance text
            ax.text(0.5, 0.02, f'Total Distance: {self.total_distance:.1f} km',
                    transform=ax.transAxes,
                    fontsize=16,
                    fontweight='bold',
                    ha='center',
                    va='bottom',
                    color='#1f2937',
                    bbox=dict(boxstyle='round,pad=0.5',
                              facecolor='white',
                              edgecolor='#e5e7eb',
                              alpha=0.9))

            # Tight layout to maximize map area
            plt.tight_layout()
            plt.subplots_adjust(left=0, right=1, top=1, bottom=0)

            # Save the image
            plt.savefig(output_path, dpi=100, bbox_inches='tight',
                        facecolor='white', edgecolor='none')
            plt.close()

            print(f"Image saved to: {output_path}")
            return True

        except Exception as e:
            print(f"Error generating image: {e}")
            return False


def main():
    """Main function to run the GPX to Image generator."""
    parser = argparse.ArgumentParser(
        description='Convert GPX files to vertical images for social media')
    parser.add_argument('gpx_file', help='Path to the GPX file')
    parser.add_argument('-o', '--output', default='route_image.png',
                        help='Output PNG file path (default: route_image.png)')
    parser.add_argument('-w', '--width', type=int, default=1080,
                        help='Image width in pixels (default: 1080)')
    parser.add_argument('--height', type=int, default=1920,
                        help='Image height in pixels (default: 1920)')
    parser.add_argument('--list-segments', action='store_true',
                        help='List all segments in the GPX file and exit')
    parser.add_argument('--reorder-segments', type=str,
                        help='Reorder segments (e.g., "2,1,3" to put segment 2 first)')

    args = parser.parse_args()

    if not os.path.exists(args.gpx_file):
        print(f"Error: GPX file '{args.gpx_file}' not found")
        return 1

    # Create generator
    generator = GPXToImageGenerator(width=args.width, height=args.height)

    # Parse GPX file
    if not generator.parse_gpx_file(args.gpx_file):
        print("Failed to parse GPX file")
        return 1

    # Handle segment listing
    if args.list_segments:
        segment_info = generator.get_segment_info()
        print("\nSegments in GPX file:")
        print("=" * 60)
        for i, info in enumerate(segment_info):
            print(f"{i+1}. {info['name']}")
            print(
                f"   Points: {info['points']} | Distance: {info['distance_km']:.1f} km")
            print(
                f"   Start: {info['start_lat']:.4f}, {info['start_lon']:.4f}")
            print(f"   End: {info['end_lat']:.4f}, {info['end_lon']:.4f}")
            print()
        print(f"Total segments: {len(segment_info)}")
        print(f"Total distance: {generator.total_distance:.2f} km")
        print("\nTo reorder segments, use: --reorder-segments \"2,1,3\"")
        return 0

    # Handle segment reordering
    if args.reorder_segments:
        try:
            # Parse the reorder string
            new_order = [int(x.strip()) -
                         1 for x in args.reorder_segments.split(',')]
            if not generator.reorder_segments(new_order):
                return 1
        except ValueError:
            print(
                "Invalid segment order format. Use comma-separated numbers (e.g., '2,1,3')")
            return 1

    # Generate image
    if not generator.generate_image(args.output):
        print("Failed to generate image")
        return 1

    print("GPX to image conversion completed successfully!")
    return 0


if __name__ == "__main__":
    exit(main())
