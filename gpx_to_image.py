#!/usr/bin/env python3
"""
GPX to Image Generator

A tool to convert GPX route files into vertical PNG images suitable for social media.
Features:
- Minimalist map background
- Distance markers every 100km
- Total distance display
- Vertical format optimized for social media
"""

import gpxpy
import gpxpy.gpx
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import io
import math
from typing import List, Tuple, Optional
import argparse
import os


class GPXToImageGenerator:
    """Main class for converting GPX files to images."""

    def __init__(self, width: int = 1080, height: int = 1920):
        """
        Initialize the generator with image dimensions.

        Args:
            width: Image width in pixels (default: 1080 for social media)
            height: Image height in pixels (default: 1920 for social media)
        """
        self.width = width
        self.height = height
        self.margin = 100  # Margin around the map
        self.track_points = []
        self.total_distance = 0.0

    def parse_gpx_file(self, gpx_file_path: str) -> bool:
        """
        Parse a GPX file and extract track points.

        Args:
            gpx_file_path: Path to the GPX file

        Returns:
            True if parsing was successful, False otherwise
        """
        try:
            with open(gpx_file_path, 'r', encoding='utf-8') as gpx_file:
                gpx = gpxpy.parse(gpx_file)

            self.track_points = []

            for track in gpx.tracks:
                for segment in track.segments:
                    for point in segment.points:
                        self.track_points.append({
                            'lat': point.latitude,
                            'lon': point.longitude,
                            'elevation': point.elevation
                        })

            if not self.track_points:
                print("No track points found in GPX file")
                return False

            self._calculate_total_distance()
            print(f"Loaded {len(self.track_points)} track points")
            print(f"Total distance: {self.total_distance:.2f} km")
            return True

        except Exception as e:
            print(f"Error parsing GPX file: {e}")
            return False

    def _calculate_total_distance(self):
        """Calculate the total distance of the route."""
        self.total_distance = 0.0

        for i in range(1, len(self.track_points)):
            prev_point = self.track_points[i-1]
            curr_point = self.track_points[i]

            distance = self._haversine_distance(
                prev_point['lat'], prev_point['lon'],
                curr_point['lat'], curr_point['lon']
            )
            self.total_distance += distance

    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        Calculate the great circle distance between two points on Earth.

        Returns:
            Distance in kilometers
        """
        R = 6371  # Earth's radius in kilometers

        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)

        a = (math.sin(delta_lat / 2) ** 2 +
             math.cos(lat1_rad) * math.cos(lat2_rad) *
             math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

        return R * c

    def _get_distance_markers(self) -> List[Tuple[float, float, float]]:
        """
        Calculate positions for distance markers every 100km.

        Returns:
            List of tuples (lat, lon, distance_km)
        """
        markers = []
        current_distance = 0.0
        next_marker_distance = 100.0

        for i in range(1, len(self.track_points)):
            prev_point = self.track_points[i-1]
            curr_point = self.track_points[i]

            segment_distance = self._haversine_distance(
                prev_point['lat'], prev_point['lon'],
                curr_point['lat'], curr_point['lon']
            )
            current_distance += segment_distance

            if current_distance >= next_marker_distance:
                markers.append((
                    curr_point['lat'],
                    curr_point['lon'],
                    next_marker_distance
                ))
                next_marker_distance += 100.0

        return markers

    def generate_image(self, output_path: str) -> bool:
        """
        Generate the final image with route, markers, and distance info.

        Args:
            output_path: Path where the PNG image will be saved

        Returns:
            True if generation was successful, False otherwise
        """
        if not self.track_points:
            print("No track points available. Parse a GPX file first.")
            return False

        try:
            # Create figure with vertical aspect ratio
            fig, ax = plt.subplots(
                figsize=(self.width/100, self.height/100), dpi=100)

            # Extract coordinates
            lats = [point['lat'] for point in self.track_points]
            lons = [point['lon'] for point in self.track_points]

            # Set up the plot bounds with margin
            lat_range = max(lats) - min(lats)
            lon_range = max(lons) - min(lons)

            # Adjust bounds to maintain aspect ratio and add margin
            margin_factor = 0.1
            ax.set_xlim(min(lons) - lon_range * margin_factor,
                        max(lons) + lon_range * margin_factor)
            ax.set_ylim(min(lats) - lat_range * margin_factor,
                        max(lats) + lat_range * margin_factor)

            # Minimalist styling
            ax.set_facecolor('#f8f9fa')  # Light gray background
            fig.patch.set_facecolor('#ffffff')  # White figure background

            # Remove axes and ticks for clean look
            ax.set_xticks([])
            ax.set_yticks([])
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['bottom'].set_visible(False)
            ax.spines['left'].set_visible(False)

            # Plot the route
            ax.plot(lons, lats, color='#2563eb',
                    linewidth=3, alpha=0.8, zorder=2)

            # Add distance markers
            markers = self._get_distance_markers()
            for lat, lon, distance in markers:
                ax.scatter(lon, lat, color='#dc2626', s=100, zorder=3,
                           edgecolors='white', linewidth=2)
                ax.annotate(f'{int(distance)}km',
                            (lon, lat),
                            xytext=(10, 10),
                            textcoords='offset points',
                            fontsize=10,
                            fontweight='bold',
                            color='#1f2937',
                            bbox=dict(boxstyle='round,pad=0.3',
                                      facecolor='white',
                                      edgecolor='none',
                                      alpha=0.8))

            # Add total distance text
            ax.text(0.5, 0.02, f'Total Distance: {self.total_distance:.1f} km',
                    transform=ax.transAxes,
                    fontsize=16,
                    fontweight='bold',
                    ha='center',
                    va='bottom',
                    color='#1f2937',
                    bbox=dict(boxstyle='round,pad=0.5',
                              facecolor='white',
                              edgecolor='#e5e7eb',
                              alpha=0.9))

            # Tight layout to maximize map area
            plt.tight_layout()
            plt.subplots_adjust(left=0, right=1, top=1, bottom=0)

            # Save the image
            plt.savefig(output_path, dpi=100, bbox_inches='tight',
                        facecolor='white', edgecolor='none')
            plt.close()

            print(f"Image saved to: {output_path}")
            return True

        except Exception as e:
            print(f"Error generating image: {e}")
            return False


def main():
    """Main function to run the GPX to Image generator."""
    parser = argparse.ArgumentParser(
        description='Convert GPX files to vertical images for social media')
    parser.add_argument('gpx_file', help='Path to the GPX file')
    parser.add_argument('-o', '--output', default='route_image.png',
                        help='Output PNG file path (default: route_image.png)')
    parser.add_argument('-w', '--width', type=int, default=1080,
                        help='Image width in pixels (default: 1080)')
    parser.add_argument('--height', type=int, default=1920,
                        help='Image height in pixels (default: 1920)')

    args = parser.parse_args()

    if not os.path.exists(args.gpx_file):
        print(f"Error: GPX file '{args.gpx_file}' not found")
        return 1

    # Create generator
    generator = GPXToImageGenerator(width=args.width, height=args.height)

    # Parse GPX file
    if not generator.parse_gpx_file(args.gpx_file):
        print("Failed to parse GPX file")
        return 1

    # Generate image
    if not generator.generate_image(args.output):
        print("Failed to generate image")
        return 1

    print("GPX to image conversion completed successfully!")
    return 0


if __name__ == "__main__":
    exit(main())
