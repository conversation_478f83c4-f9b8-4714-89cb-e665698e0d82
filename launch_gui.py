#!/usr/bin/env python3
"""
Simple launcher for the GPX to Image Generator GUI
"""

import sys
import os

# Add current directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gpx_to_image_gui import main
    main()
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please make sure all dependencies are installed:")
    print("pip install -r requirements.txt")
    input("Press Enter to exit...")
except Exception as e:
    print(f"Error running GUI: {e}")
    input("Press Enter to exit...")
