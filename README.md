# GPX to Image Generator

A Python tool that converts GPX route files into vertical PNG images optimized for social media sharing.

## Features

- **Minimalist Design**: Clean, modern map visualization with minimal distractions
- **Distance Markers**: Automatic markers every 100km along the route
- **Total Distance Display**: Shows the complete route distance
- **Social Media Ready**: Vertical format (1080x1920) perfect for Instagram, TikTok, etc.
- **Customizable**: Adjustable image dimensions and styling

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

```bash
python gpx_to_image.py your_route.gpx
```

This will create a `route_image.png` file in the current directory.

### Advanced Usage

```bash
python gpx_to_image.py your_route.gpx -o custom_output.png -w 1080 -h 1920
```

### Command Line Options

- `gpx_file`: Path to your GPX file (required)
- `-o, --output`: Output PNG file path (default: `route_image.png`)
- `-w, --width`: Image width in pixels (default: 1080)
- `-h, --height`: Image height in pixels (default: 1920)

## Example

```bash
# Convert a GPX file to a social media ready image
python gpx_to_image.py my_bike_route.gpx -o my_route_image.png

# Create a custom sized image
python gpx_to_image.py long_distance_route.gpx -o route.png -w 1200 -h 2000
```

## Output

The generated image will include:

- Your complete route plotted as a blue line
- Red markers every 100km with distance labels
- Total distance displayed at the bottom
- Clean, minimalist map background
- Vertical format optimized for social media

## Supported GPX Files

This tool supports standard GPX files with track data. It will automatically:

- Parse all tracks and segments in the file
- Calculate distances using the Haversine formula
- Handle multiple track segments
- Extract elevation data (if available)

## Requirements

- Python 3.7+
- gpxpy
- matplotlib
- Pillow (PIL)
- numpy

## License

This project is open source and available under the MIT License.
