# GPX to Image Generator

A Python tool that converts GPX route files into vertical PNG images optimized for social media sharing.

## Features

- **Minimalist Design**: Clean, modern map visualization with minimal distractions
- **Distance Markers**: Automatic markers every 100km along the route
- **Total Distance Display**: Shows the complete route distance
- **Social Media Ready**: Vertical format (1080x1920) perfect for Instagram, TikTok, etc.
- **Customizable**: Adjustable image dimensions and styling

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Interactive GUI (Recommended)

For the easiest experience, use the graphical interface:

```bash
python gpx_to_image_gui.py
```

Or use the launcher:

```bash
python launch_gui.py
```

The GUI provides:

- Easy file selection with browse buttons
- Visual dimension presets (Instagram, Square, Landscape)
- **Segment reordering** - Reorder GPX segments with drag-and-drop interface
- Real-time progress updates
- Automatic output folder opening

### Command Line Usage

#### Basic Usage

```bash
python gpx_to_image.py your_route.gpx
```

This will create a `route_image.png` file in the current directory.

#### Advanced Usage

```bash
python gpx_to_image.py your_route.gpx -o custom_output.png -w 1080 --height 1920
```

### Command Line Options

- `gpx_file`: Path to your GPX file (required)
- `-o, --output`: Output PNG file path (default: `route_image.png`)
- `-w, --width`: Image width in pixels (default: 1080)
- `--height`: Image height in pixels (default: 1920)
- `--list-segments`: List all segments in the GPX file and exit
- `--reorder-segments`: Reorder segments (e.g., "2,1,3" to put segment 2 first)

## Example

```bash
# Convert a GPX file to a social media ready image
python gpx_to_image.py my_bike_route.gpx -o my_route_image.png

# Create a custom sized image
python gpx_to_image.py long_distance_route.gpx -o route.png -w 1200 -h 2000

# List segments in a GPX file
python gpx_to_image.py my_route.gpx --list-segments

# Reorder segments (put segment 3 first, then 1, then 2)
python gpx_to_image.py my_route.gpx --reorder-segments "3,1,2" -o reordered_route.png
```

## Segment Reordering

Sometimes GPX files have segments in the wrong order, or you want to rearrange them for better visualization. This tool provides both command-line and GUI options for reordering segments.

### Command Line Segment Reordering

1. **List segments** to see what's available:

   ```bash
   python gpx_to_image.py your_route.gpx --list-segments
   ```

2. **Reorder segments** using the segment numbers:
   ```bash
   python gpx_to_image.py your_route.gpx --reorder-segments "2,1,3" -o reordered.png
   ```

### GUI Segment Reordering

1. Load your GPX file using the "Browse..." button
2. Click the **"Reorder Segments"** button (enabled after loading a GPX file)
3. Use the dialog to:
   - View all segments with their details (points, distance, start/end coordinates)
   - Move segments up/down using the buttons
   - Reset to original order if needed
   - Apply the new order

The segment reordering is especially useful for:

- **Multi-day trips** where segments were recorded out of order
- **Loop routes** where you want to change the starting point
- **Complex routes** with multiple disconnected segments

## Output

The generated image will include:

- Your complete route plotted as a blue line
- Red markers every 100km with distance labels
- Total distance displayed at the bottom
- Clean, minimalist map background
- Vertical format optimized for social media

## Supported GPX Files

This tool supports standard GPX files with track data. It will automatically:

- Parse all tracks and segments in the file
- Calculate distances using the Haversine formula
- Handle multiple track segments
- Extract elevation data (if available)

## Requirements

- Python 3.7+
- gpxpy
- matplotlib
- Pillow (PIL)
- numpy

## License

This project is open source and available under the MIT License.
