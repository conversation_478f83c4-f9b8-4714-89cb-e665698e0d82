#!/usr/bin/env python3
"""
Test script for segment naming functionality
"""

from gpx_to_image import GPXToImageGenerator

def test_segment_naming():
    """Test the segment naming functionality."""
    
    print("Testing Segment Naming...")
    print("=" * 50)
    
    # Test with international route
    generator = GPXToImageGenerator()
    
    print("Loading international route...")
    if generator.parse_gpx_file("international_route.gpx"):
        segment_info = generator.get_segment_info()
        
        print(f"\nFound {len(segment_info)} segments:")
        print("-" * 40)
        
        for i, info in enumerate(segment_info):
            print(f"{i+1}. {info['name']}")
            print(f"   Distance: {info['distance_km']:.1f} km")
            print(f"   Points: {info['points']}")
            print()
        
        print("✓ Segment naming working correctly!")
        
        # Test reordering
        print("Testing segment reordering...")
        original_names = [info['name'] for info in segment_info]
        
        # Reorder: 2,1,4,3
        if generator.reorder_segments([1, 0, 3, 2]):
            new_info = generator.get_segment_info()
            new_names = [info['name'] for info in new_info]
            
            print("Original order:")
            for i, name in enumerate(original_names):
                print(f"  {i+1}. {name}")
            
            print("\nNew order:")
            for i, name in enumerate(new_names):
                print(f"  {i+1}. {name}")
            
            print("✓ Segment reordering working correctly!")
        else:
            print("✗ Segment reordering failed")
            
    else:
        print("✗ Failed to load GPX file")

if __name__ == "__main__":
    test_segment_naming()
